import React from 'react';
import { SectionPanel, SectionPanelRow } from '@ceridianhcm/components';
import { IEmployeeLetter } from '@/models';
import { SingleLetterContent } from './components/Content/SingleLetterContent';
import { SingleLetterNotificationBanner } from './components/NotficationBanner/SingleLetterNotificationBanner';

import './single-letter.scss';

export interface SingleLetterProps {
  id: string;
  testId?: string;
  selectedLetter?: IEmployeeLetter | null;
}

export const SingleLetter: React.FC<SingleLetterProps> = ({
  id = 'single-letter',
  testId = 'single-letter-test',
  selectedLetter,
}) => {
  if (!selectedLetter) {
    return <span>No letter selected</span>;
  }

  return (
    <div className="single-letter-container" data-testid={testId}>
      <SectionPanel id={`${id}-section-panel`} testId={`${testId}-section-panel`}>
        <SectionPanelRow id={`${id}-notification-panel-row`} testId={`${testId}-notification-panel-row`}>
          <SingleLetterNotificationBanner
            id={`${id}-notification`}
            testId={`${testId}-notification`}
            selectedLetter={selectedLetter}
          />
        </SectionPanelRow>
        <SectionPanelRow id={`${id}-content-panel-row`} testId={`${testId}-content-panel-row`}>
          <SingleLetterContent
            id={`${id}-content-section`}
            testId={`${testId}-content-panel-row`}
            selectedLetter={selectedLetter}
          />
        </SectionPanelRow>
      </SectionPanel>
    </div>
  );
};
