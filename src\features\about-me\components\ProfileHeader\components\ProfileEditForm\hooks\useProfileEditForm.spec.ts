<<<<<<< HEAD
import { renderHook, waitFor } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { useProfileEditForm } from './useProfileEditForm';
import { getEmployeePronouns } from '@/api/employeeApi';
import { EmployeePronounsMockData } from '@/mocks/employee';

// Mock the API
jest.mock('@/api/employeeApi', () => ({
  getEmployeePronouns: jest.fn(),
}));

// Mock the profile image service
=======
import { renderHook } from '@testing-library/react-hooks';
import { act } from '@testing-library/react';
import { useProfileEditForm } from './useProfileEditForm';
import { profileImageService } from '../services/profileImageService';

// Mock the profileImageService
>>>>>>> main
jest.mock('../services/profileImageService', () => ({
  profileImageService: {
    uploadImage: jest.fn(),
    removeImage: jest.fn(),
  },
}));

<<<<<<< HEAD
const mockGetEmployeePronouns = getEmployeePronouns as jest.MockedFunction<typeof getEmployeePronouns>;

=======
>>>>>>> main
describe('useProfileEditForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

<<<<<<< HEAD
  describe('pronoun options integration', () => {
    it('should fetch and transform pronoun options on mount', async () => {
      // Arrange
      mockGetEmployeePronouns.mockResolvedValue(EmployeePronounsMockData);

      // Act
      const { result } = renderHook(() => useProfileEditForm());

      // Assert
      await waitFor(() => {
        expect(result.current.state.pronounOptions).toHaveLength(4);
      });

      expect(result.current.state.pronounOptions).toEqual([
        { id: 'SHEHERHERS', title: 'She/Her/Hers' },
        { id: 'HEHIMHIS', title: 'He/Him/His' },
        { id: 'THEYTHEMTHEIRS', title: 'They/Them/Theirs' },
        { id: 'ZEPERHIRTHEY', title: 'Ze/Per/Hir/They' },
      ]);
    });

    it('should handle API error gracefully', async () => {
      // Arrange
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      mockGetEmployeePronouns.mockRejectedValue(new Error('API Error'));

      // Act
      const { result } = renderHook(() => useProfileEditForm());

      // Assert
      await waitFor(() => {
        expect(result.current.state.pronounOptions).toEqual([]);
      });

      expect(consoleErrorSpy).toHaveBeenCalledWith('Failed to fetch pronoun options:', expect.any(Error));

      consoleErrorSpy.mockRestore();
    });

    it('should call getEmployeePronouns API on mount', async () => {
      // Arrange
      mockGetEmployeePronouns.mockResolvedValue(EmployeePronounsMockData);

      // Act
      renderHook(() => useProfileEditForm());

      // Assert
      expect(mockGetEmployeePronouns).toHaveBeenCalledTimes(1);
    });
  });

  describe('initial state', () => {
    it('should initialize with empty pronoun options', () => {
      // Arrange
      mockGetEmployeePronouns.mockImplementation(() => new Promise(() => {})); // Never resolves

      // Act
      const { result } = renderHook(() => useProfileEditForm());

      // Assert
      expect(result.current.state.pronounOptions).toEqual([]);
      expect(result.current.state.isLoading).toBe(false);
      expect(result.current.state.error).toBe(null);
      expect(result.current.state.photoData).toBe(null);
    });
  });

  describe('with form methods', () => {
    it('should work with react-hook-form methods', async () => {
      // Arrange
      mockGetEmployeePronouns.mockResolvedValue(EmployeePronounsMockData);

      const { result: formResult } = renderHook(() => useForm());
      const formMethods = formResult.current;

      // Act
      const { result } = renderHook(() => useProfileEditForm(formMethods));

      // Assert
      await waitFor(() => {
        expect(result.current.state.pronounOptions).toHaveLength(4);
      });

      expect(result.current.actions.submitProfile).toBeDefined();
      expect(result.current.actions.handlePhotoUpload).toBeDefined();
      expect(result.current.actions.handlePhotoRemove).toBeDefined();
=======
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useProfileEditForm());

    expect(result.current.state).toEqual({
      isLoading: false,
      error: null,
      photoData: null,
    });
  });

  describe('handlePhotoUpload', () => {
    it('should handle successful photo upload', async () => {
      const mockImageData = 'data:image/jpeg;base64,test123';
      (profileImageService.uploadImage as jest.Mock).mockResolvedValueOnce({
        imageData: mockImageData,
      });

      const { result } = renderHook(() => useProfileEditForm());

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      let uploadedImageData;
      await act(async () => {
        uploadedImageData = await result.current.actions.handlePhotoUpload(file);
      });

      expect(uploadedImageData).toBe(mockImageData);
      expect(result.current.state.photoData).toBe(mockImageData);
      expect(result.current.state.error).toBeNull();
      expect(result.current.state.isLoading).toBeFalsy();
    });

    it('should handle photo upload error', async () => {
      const errorMessage = 'Upload failed';
      (profileImageService.uploadImage as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useProfileEditForm());

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      await act(async () => {
        try {
          await result.current.actions.handlePhotoUpload(file);
        } catch (error) {
          // Expected error
        }
      });

      expect(result.current.state.error).toBe(errorMessage);
      expect(result.current.state.isLoading).toBeFalsy();
    });
  });

  describe('handlePhotoRemove', () => {
    it('should handle successful photo removal', async () => {
      (profileImageService.removeImage as jest.Mock).mockResolvedValueOnce({ success: true });

      const { result } = renderHook(() => useProfileEditForm());

      await act(async () => {
        await result.current.actions.handlePhotoRemove();
      });

      expect(result.current.state.photoData).toBeNull();
      expect(result.current.state.error).toBeNull();
      expect(result.current.state.isLoading).toBeFalsy();
    });

    it('should handle photo removal error', async () => {
      const errorMessage = 'Remove failed';
      (profileImageService.removeImage as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useProfileEditForm());

      await act(async () => {
        try {
          await result.current.actions.handlePhotoRemove();
        } catch (error) {
          // Expected error
        }
      });

      expect(result.current.state.error).toBe(errorMessage);
      expect(result.current.state.isLoading).toBeFalsy();
    });
  });

  describe('submitProfile', () => {
    it('should handle successful profile submission', async () => {
      const { result } = renderHook(() => useProfileEditForm());

      const testData = {
        profilePhoto: 'test.jpg',
        pronouns: 'they/them',
        biography: 'test bio',
      };

      await act(async () => {
        await result.current.actions.submitProfile(testData);
      });

      expect(result.current.state.error).toBeNull();
      expect(result.current.state.isLoading).toBeFalsy();
    });

    it('should include photoData in submission if available', async () => {
      const consoleSpy = jest.spyOn(console, 'log');
      const { result } = renderHook(() => useProfileEditForm());

      // Set photo data first
      const mockImageData = 'data:image/jpeg;base64,test123';
      (profileImageService.uploadImage as jest.Mock).mockResolvedValueOnce({
        imageData: mockImageData,
      });

      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      await act(async () => {
        await result.current.actions.handlePhotoUpload(file);
      });

      const testData = {
        profilePhoto: 'old.jpg',
        pronouns: 'they/them',
        biography: 'test bio',
      };

      await act(async () => {
        await result.current.actions.submitProfile(testData);
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Submitting profile:',
        expect.objectContaining({
          ...testData,
          profilePhoto: mockImageData,
        }),
      );

      consoleSpy.mockRestore();
>>>>>>> main
    });
  });
});
