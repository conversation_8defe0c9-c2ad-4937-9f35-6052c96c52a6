/**
 * Service for handling profile image operations.
 * This service handles image upload and removal operations for the profile edit feature.
 */

interface ImageUploadResponse {
  imageData: string;
}

interface ImageRemoveResponse {
  success: boolean;
}

/**
 * Converts a File object to a base64 string
 * @param file The file to convert
 * @returns Promise containing the base64 string
 */
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
};

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
<<<<<<< HEAD
=======

// Mock API call that will be replaced with real implementation
const removeProfileImage = async (): Promise<{ success: boolean }> => {
  return Promise.resolve({ success: true });
};

>>>>>>> main
export const profileImageService = {
  /**
   * Uploads a profile image
   * @param file The file to upload
   * @returns Promise containing the uploaded image data
   * @throws Error if the upload fails
   */
  uploadImage: async (file: File): Promise<ImageUploadResponse> => {
    try {
      if (!file.type.startsWith('image/')) {
        throw new Error('Unsupported file type');
      }
      if (file.size > MAX_FILE_SIZE) {
        throw new Error('File size exceeds 5MB limit');
      }
      const imageData = await fileToBase64(file);
      return { imageData };
    } catch (error) {
      throw new Error('Failed to process image upload');
    }
  },

  /**
   * Removes the current profile image
   * @returns Promise indicating success
   * @throws Error if the removal fails
   */
  removeImage: async (): Promise<ImageRemoveResponse> => {
    try {
<<<<<<< HEAD
      // TODO: Replace with actual API call when available
      return { success: true };
=======
      const response = await removeProfileImage();
      return response;
>>>>>>> main
    } catch (error) {
      throw new Error('Failed to remove profile image');
    }
  },
};
