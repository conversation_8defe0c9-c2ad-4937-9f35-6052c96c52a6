import { TIconName } from '@ceridianhcm/components';
import { stripHtmlTags } from '@utils/sanitizeUtils';
import { IEmployeeCertificationNoteResponse } from '@features/qualifications/models';
import { getFileIcon } from '@utils/fileUtils';

export interface INoteCardDataVM {
  id: string;
  avatarSrc: string;
  avatarAlt: string;
  title: string;
  subtitle: string;
  tagLabel?: string;
  tagStatus?: 'info' | 'warning' | 'error' | 'success';
  notesHeading: string;
  notesSubHeading: string;
  attachments: Array<{
    id: string;
    href: string;
    label: string;
    iconName: TIconName;
  }>;
}

const MOCK_AVATAR_URLS = [
  '/assets/images/user-ada.png',
  '/assets/images/user-george.png',
  '/assets/images/user-taro.jpg',
] as const;

const getAvatarUrl = (userId: number): string => {
  // Use modulo to get a consistent index based on user ID
  const index = userId % MOCK_AVATAR_URLS.length;
  return MOCK_AVATAR_URLS[index];
};

export const mapNotesToTableVM = (response: IEmployeeCertificationNoteResponse): INoteCardDataVM[] => {
  if (!response?.EntityLists?.[0]?.Entities) {
    return [];
  }

  return response.EntityLists[0].Entities.map((note) => ({
    id: note.LMSEmployeeCertificationNoteId.toString(),
    avatarSrc: getAvatarUrl(note.CreatedUserId), // replace with actual avatar URL via service call
    avatarAlt: `${note.CreatedUserDisplayName} Avatar`,
    title: note.CreatedUserDisplayName,
    subtitle: stripHtmlTags(note.CreatedTimestampString),
    tagLabel: note.CanViewCreatedUser ? 'Manager' : undefined,
    tagStatus: note.CanViewCreatedUser ? 'info' : undefined,
    notesHeading: note.Title || 'Untitled Note',
    notesSubHeading: note.Comment || '',
    attachments:
      note.Attachments?.map((attachment) => ({
        id: attachment.DocMgmtFileStoreId.toString(),
        href: `/api/files/${attachment.DocMgmtFileStoreId}`, // update with actual file download path
        label: attachment.FileName,
        iconName: getFileIcon(attachment.FileName),
      })) || [],
  }));
};
