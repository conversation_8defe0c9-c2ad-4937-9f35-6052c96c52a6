import { useReducer } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ProfileEditFormData } from '../profile-edit-form-schema';
import { profileImageService } from '../services/profileImageService';

interface State {
  isLoading: boolean;
  error: string | null;
  photoData: string | null;
  pronounOptions: { title: string; id: string }[];
}

type Action =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_PHOTO_DATA'; payload: string | null }
  | { type: 'RESET_STATE' };

const initialState: State = {
  isLoading: false,
  error: null,
  photoData: null,
  pronounOptions:[]
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_PHOTO_DATA':
      return { ...state, photoData: action.payload };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
}

/**
 * Hook for managing profile edit logic with sophisticated state management.
 * This hook is designed to work with react-hook-form and provides centralized state management
 * for profile editing functionality.
 *
 * Features:
 * - Photo upload and removal handling
 * - Form state management
 * - Loading and error states
 * - Profile submission
 *
 * @param methods - Optional UseFormReturn instance from react-hook-form
 * @returns {Object} state and actions for profile editing
 *
 * @example
 * ```tsx
 * const methods = useForm<ProfileEditFormData>();
 * const { state, actions } = useProfileEditForm(methods);
 *
 * // Access state
 * const { isLoading, error, photoData } = state;
 *
 * // Use actions
 * await actions.handlePhotoUpload(file);
 * await actions.submitProfile(data);
 * ```
 */
export const useProfileEditForm = (methods?: UseFormReturn<ProfileEditFormData>) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const startLoading = () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });
  };

  const stopLoading = () => {
    dispatch({ type: 'SET_LOADING', payload: false });
  };

  const setError = (error: string) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const handlePhotoUpload = async (file: File): Promise<string> => {
    startLoading();
    try {
      const { imageData } = await profileImageService.uploadImage(file);
      dispatch({ type: 'SET_PHOTO_DATA', payload: imageData });
      return imageData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload photo';
      setError(errorMessage);
      throw err;
    } finally {
      stopLoading();
    }
  };

  const handlePhotoRemove = async (): Promise<void> => {
    startLoading();
    try {
      await profileImageService.removeImage();
      dispatch({ type: 'SET_PHOTO_DATA', payload: null });
      methods?.setValue('profilePhoto', '');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove photo';
      setError(errorMessage);
      throw err;
    } finally {
      stopLoading();
    }
  };

  const submitProfile = async (data: ProfileEditFormData): Promise<void> => {
    startLoading();
    try {
      const submitData = {
        ...data,
        profilePhoto: state.photoData || data.profilePhoto,
      };

      // Simulate API call
      await new Promise((resolve) => {
        setTimeout(resolve, 1000);
      });
      console.log('Submitting profile:', submitData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';
      setError(errorMessage);
      throw err;
    } finally {
      stopLoading();
    }
  };

  return {
    state,
    actions: {
      handlePhotoUpload,
      handlePhotoRemove,
      submitProfile,
    },
  };
};
