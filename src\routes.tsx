import React, { ReactNode } from 'react';
import { LetterStartSection, LetterEndSection } from '@features/letters/components/OmniBarSections';

const AboutMe = React.lazy(() => import('./pages/AboutMe'));
const Apps = React.lazy(() => import('./pages/Apps'));
const CareerProfile = React.lazy(() => import('./pages/CareerProfile'));
const Forms = React.lazy(() => import('./pages/Forms'));
const Learning = React.lazy(() => import('./pages/Learning'));
const Letters = React.lazy(() => import('./pages/Letters'));
const Notifications = React.lazy(() => import('./pages/Notifications'));
const Preferences = React.lazy(() => import('./pages/Preferences'));
const Qualifications = React.lazy(() => import('./pages/Qualifications'));
const Security = React.lazy(() => import('./pages/Security'));

export interface Route {
  id: string;
  label: string;
  component?: React.ComponentType<any>;
  iconName: string;
  active?: boolean;
  startSection?: ReactNode;
  endSection?: ReactNode;
  childRoutes?: Route[];
  testId?: string;
  hideFromNav?: boolean;
}

export type RouteConfig = Route[];

export const routeConfig: RouteConfig = [
  {
    id: 'personal',
    iconName: 'person',
    label: 'Personal',
    active: true,
    testId: 'sidenav-personal-test-id',
    childRoutes: [
      {
        id: 'about-me',
        label: 'About me',
        component: AboutMe,
        active: true,
        iconName: '',
        testId: 'sidenav-about-me-test-id',
      },
      {
        id: 'letters',
        label: 'Letters',
        component: Letters,
        iconName: '',
        active: false,
        testId: 'sidenav-letters-test-id',
      },
      {
        id: 'single-letter',
        label: 'Letter Detail',
        component: Letters,
        iconName: '',
        active: false,
        startSection: <LetterStartSection iconName="arrowLeft" />,
        endSection: <LetterEndSection />,
        testId: 'letter-detail-test-id',
        hideFromNav: true,
      },
    ],
  },
  {
    id: 'career',
    iconName: 'briefcase',
    label: 'Career',
    testId: 'sidenav-career-test-id',
    childRoutes: [
      {
        id: 'career-profile',
        label: 'Career profile',
        component: CareerProfile,
        iconName: '',
        testId: 'sidenav-career-profile-test-id',
      },
      {
        id: 'qualifications',
        label: 'Qualifications',
        component: Qualifications,
        iconName: '',
        testId: 'sidenav-qualifications-test-id',
      },
      {
        id: 'learning',
        label: 'Learning',
        component: Learning,
        iconName: '',
        testId: 'sidenav-learning-test-id',
      },
    ],
  },
  {
    id: 'forms',
    iconName: 'form',
    label: 'Forms',
    component: Forms,
    testId: 'sidenav-forms-test-id',
  },
  {
    id: 'settings',
    iconName: 'settings',
    label: 'Settings',
    testId: 'sidenav-settings-test-id',
    childRoutes: [
      {
        id: 'preferences',
        label: 'Preferences',
        component: Preferences,
        iconName: '',
        testId: 'sidenav-preferences-test-id',
      },
      {
        id: 'apps',
        label: 'Apps',
        component: Apps,
        iconName: '',
        testId: 'sidenav-apps-test-id',
      },
      {
        id: 'notifications',
        label: 'Notifications',
        component: Notifications,
        iconName: '',
        testId: 'sidenav-notifications-test-id',
      },
      {
        id: 'security',
        label: 'Security',
        component: Security,
        iconName: '',
        testId: 'sidenav-security-test-id',
      },
    ],
  },
];
