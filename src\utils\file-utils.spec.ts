import { getFileIcon } from './fileUtils';

describe('fileUtils', () => {
  describe('getFileIcon', () => {
    // Test PDF files
    it('should return pdf icon for PDF files', () => {
      expect(getFileIcon('document.pdf')).toBe('pdf');
      expect(getFileIcon('report.PDF')).toBe('pdf');
      expect(getFileIcon('file.with.multiple.dots.pdf')).toBe('pdf');
    });

    // Test image files
    it('should return image icon for PNG files', () => {
      expect(getFileIcon('image.png')).toBe('image');
      expect(getFileIcon('screenshot.PNG')).toBe('image');
    });

    it('should return image icon for JPG files', () => {
      expect(getFileIcon('photo.jpg')).toBe('image');
      expect(getFileIcon('picture.JPG')).toBe('image');
    });

    it('should return image icon for JPEG files', () => {
      expect(getFileIcon('photo.jpeg')).toBe('image');
      expect(getFileIcon('picture.JPEG')).toBe('image');
    });

    // Test file icon cases
    it('should return file icon for ZIP files', () => {
      expect(getFileIcon('archive.zip')).toBe('file');
      expect(getFileIcon('files.ZIP')).toBe('file');
    });

    it('should return file icon for TXT files', () => {
      expect(getFileIcon('notes.txt')).toBe('file');
      expect(getFileIcon('readme.TXT')).toBe('file');
    });

    // Test default case
    it('should return file icon for unknown extensions', () => {
      expect(getFileIcon('document.docx')).toBe('file');
      expect(getFileIcon('presentation.pptx')).toBe('file');
      expect(getFileIcon('spreadsheet.xlsx')).toBe('file');
    });

    // Edge cases
    it('should handle files without extensions', () => {
      expect(getFileIcon('filename')).toBe('file');
    });

    it('should handle files with empty names', () => {
      expect(getFileIcon('')).toBe('file');
    });

    it('should handle files with only extension', () => {
      expect(getFileIcon('.pdf')).toBe('pdf');
      expect(getFileIcon('.jpg')).toBe('image');
    });
  });
});
